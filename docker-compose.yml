version: '3.8'

services:
  minio:
    image: minio/minio:latest
    container_name: minio
    ports:
      - "9000:9000"      # MinIO API
    environment:
      MINIO_ROOT_USER: "sysadmin"
      MINIO_ROOT_PASSWORD: "Thinklabs@36"
      MINIO_IDENTITY_PLUGIN_URL: "https://qldd.npt.com.vn/storage/api/v1/credentials"
      MINIO_IDENTITY_PLUGIN_ROLE_POLICY: "consoleAdmin"
      MINIO_IDENTITY_PLUGIN_ROLE_ID: "thinklabsMinioRoleArn"

    volumes:
      - minioapi:/data
    command: server /data
    restart: unless-stopped
    networks:
      - minio-network

volumes:
  minioapi:
    driver: ovarlay

networks:
  minio-network:
    external: true
