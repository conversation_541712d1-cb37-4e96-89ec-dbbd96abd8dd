version: '3.8'

services:
  minio:
    image: minio/minio:latest
    container_name: minio
    ports:
      - "9000:9000"      # MinIO API
      - "9001:9001"      # MinIO Console
    environment:
      MINIO_ROOT_USER: "sysadmin"
      MINIO_ROOT_PASSWORD: "Thinklabs@36"
      MINIO_IDENTITY_PLUGIN_URL: "https://qldd.npt.com.vn/storage/api/v1/credentials"
      MINIO_IDENTITY_PLUGIN_ROLE_POLICY: "consoleAdmin"
      MINIO_IDENTITY_PLUGIN_ROLE_ID: "thinklabsMinioRoleArn"

    volumes:
      - minioapi:/data
    command: server /data --console-address ":9001"
    restart: unless-stopped

volumes:
  minioapi:
    driver: local

networks:
  minio-net:
    driver: overlay
