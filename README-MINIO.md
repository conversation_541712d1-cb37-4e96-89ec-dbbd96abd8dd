# Hướng dẫn cài đặt MinIO với Docker

## Giới thiệu
MinIO là một object storage server tương thích với Amazon S3 API, được thiết kế để chạy trên các môi trường cloud-native và containerized.

## Cài đặt và chạy

### 1. Khởi động MinIO
```bash
docker-compose up -d
```

### 2. Ki<PERSON>m tra trạng thái
```bash
docker-compose ps
```

### 3. Xem logs
```bash
docker-compose logs -f minio
```

## Truy cập MinIO

### MinIO Console (Web UI)
- URL: http://localhost:9001
- Username: `minioadmin`
- Password: `minioadmin123`

### MinIO API
- URL: http://localhost:9000
- Access Key: `minioadmin`
- Secret Key: `minioadmin123`

## Cấu hình

### Thay đổi thông tin đăng nhập
Chỉnh sửa file `docker-compose.yml`:
```yaml
environment:
  MINIO_ROOT_USER: your_username
  MINIO_ROOT_PASSWORD: your_secure_password
```

### Ports
- `9000`: MinIO API endpoint
- `9001`: MinIO Console (Web interface)

## Dữ liệu
Dữ liệu được lưu trữ trong Docker volume `minio_data` để đảm bảo persistence.

## Lệnh hữu ích

### Dừng MinIO
```bash
docker-compose down
```

### Dừng và xóa dữ liệu
```bash
docker-compose down -v
```

### Restart MinIO
```bash
docker-compose restart minio
```

## Sử dụng MinIO Client (mc)

### Cài đặt mc
```bash
# Linux/macOS
curl https://dl.min.io/client/mc/release/linux-amd64/mc -o mc
chmod +x mc
sudo mv mc /usr/local/bin/

# Windows (PowerShell)
Invoke-WebRequest -Uri "https://dl.min.io/client/mc/release/windows-amd64/mc.exe" -OutFile "mc.exe"
```

### Cấu hình mc
```bash
mc alias set local http://localhost:9000 minioadmin minioadmin123
```

### Tạo bucket
```bash
mc mb local/my-bucket
```

### Upload file
```bash
mc cp myfile.txt local/my-bucket/
```

### List buckets
```bash
mc ls local
```
