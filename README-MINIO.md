# Hướng dẫn cài đặt MinIO với Docker

## Giới thiệu
MinIO là một object storage server tương thích với Amazon S3 API, đư<PERSON><PERSON> thiết kế để chạy trên các môi trường cloud-native và containerized.

## Cài đặt và chạy

### 1. Khởi động MinIO
```bash
docker-compose up -d
```

### 2. Ki<PERSON>m tra trạng thái
```bash
docker-compose ps
```

### 3. Xem logs
```bash
docker-compose logs -f minio
```

## Truy cập MinIO

### MinIO API
- URL: http://localhost:9000
- Access Key: `sysadmin`
- Secret Key: `Thinklabs@36`

**Lưu ý**: Cấu hình này chỉ chạy MinIO API mà không có Web Console để tiết kiệm tài nguyên cho môi trường local.

## Cấu hình

### Thay đổi thông tin đăng nhập
Chỉnh sửa file `docker-compose.yml`:
```yaml
environment:
  MINIO_ROOT_USER: your_username
  MINIO_ROOT_PASSWORD: your_secure_password
  MINIO_IDENTITY_PLUGIN_URL: https://your_domain/storage/api/v1/credentials
```

### Ports
- `9000`: MinIO API endpoint

### Cấu hình Identity Plugin
Cấu hình hiện tại sử dụng identity plugin để xác thực:
- `MINIO_IDENTITY_PLUGIN_URL`: URL endpoint cho xác thực
- `MINIO_IDENTITY_PLUGIN_ROLE_POLICY`: Policy role
- `MINIO_IDENTITY_PLUGIN_ROLE_ID`: Role ID

## Dữ liệu
Dữ liệu được lưu trữ trong Docker volume `minioapi` để đảm bảo persistence.

## Network
MinIO được cấu hình để chạy trong mạng `minio-net` với driver overlay. Để kết nối với tRadar và các dịch vụ khác:

### Cách 1: Sử dụng external network (Khuyến nghị)
Nếu tRadar đã có network riêng, bạn có thể sử dụng external network:
```yaml
networks:
  tradar-network:
    external: true
```

### Cách 2: Tạo shared network
Tạo một network chung cho cả MinIO và tRadar:
```yaml
networks:
  shared-network:
    driver: bridge
```

### Cách 3: Sử dụng host network
Để đơn giản nhất, có thể sử dụng host network:
```yaml
services:
  minio:
    network_mode: "host"
```