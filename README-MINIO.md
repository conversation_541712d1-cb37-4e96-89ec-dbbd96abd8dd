# Hướng dẫn cài đặt MinIO với Docker Swarm

## Giới thiệu
MinIO là một object storage server tương thích với Amazon S3 API, đư<PERSON><PERSON> thiết kế để chạy trên các môi trường cloud-native và containerized. Cấu hình này được tối ưu cho Docker Swarm.

## Cài đặt và chạy

### 1. Tạo network (nếu chưa có)
```bash
docker network create --driver overlay tradar-network
```

### 2. Deploy MinIO stack
```bash
docker stack deploy -c docker-compose.yml minio-stack
```

### 3. Kiểm tra trạng thái
```bash
docker stack services minio-stack
docker service ps minio-stack_minio
```

### 4. Xem logs
```bash
docker service logs -f minio-stack_minio
```

## Truy cập MinIO

### MinIO API
- URL: http://localhost:9000
- Access Key: `sysadmin`
- Secret Key: `Thinklabs@36`

**L<PERSON>u ý**: <PERSON><PERSON><PERSON> hình này chỉ chạy MinIO API mà không có Web Console để tiết kiệm tài nguyên cho môi trường local.

## Cấu hình

### Thay đổi thông tin đăng nhập
Chỉnh sửa file `docker-compose.yml`:
```yaml
environment:
  MINIO_ROOT_USER: your_username
  MINIO_ROOT_PASSWORD: your_secure_password
  MINIO_IDENTITY_PLUGIN_URL: https://your_domain/storage/api/v1/credentials
```

### Ports
- `9000`: MinIO API endpoint

### Cấu hình Identity Plugin
Cấu hình hiện tại sử dụng identity plugin để xác thực:
- `MINIO_IDENTITY_PLUGIN_URL`: URL endpoint cho xác thực
- `MINIO_IDENTITY_PLUGIN_ROLE_POLICY`: Policy role
- `MINIO_IDENTITY_PLUGIN_ROLE_ID`: Role ID

## Dữ liệu
Dữ liệu được lưu trữ trong Docker volume `minioapi` để đảm bảo persistence.

## Network
MinIO được cấu hình để chạy trong mạng `minio-net` với driver overlay. Để kết nối với tRadar và các dịch vụ khác:

### Cách 1: Sử dụng external network (Khuyến nghị)
Nếu tRadar đã có network riêng, bạn có thể sử dụng external network:
```yaml
networks:
  tradar-network:
    external: true
```

### Cách 2: Tạo shared network
Tạo một network chung cho cả MinIO và tRadar:
```yaml
networks:
  shared-network:
    driver: bridge
```

### Cách 3: Sử dụng host network
Để đơn giản nhất, có thể sử dụng host network:
```yaml
services:
  minio:
    network_mode: "host"
```

## Lệnh hữu ích cho Docker Swarm

### Dừng MinIO stack
```bash
docker stack rm minio-stack
```

### Update service
```bash
docker service update minio-stack_minio
```

### Scale service (nếu cần)
```bash
docker service scale minio-stack_minio=1
```

### Xem thông tin chi tiết
```bash
docker service inspect minio-stack_minio
```

### Xem nodes trong swarm
```bash
docker node ls
```

## Sử dụng MinIO Client (mc) với Docker Swarm

### Cài đặt mc
```bash
# Linux/macOS
curl https://dl.min.io/client/mc/release/linux-amd64/mc -o mc
chmod +x mc
sudo mv mc /usr/local/bin/

# Windows (PowerShell)
Invoke-WebRequest -Uri "https://dl.min.io/client/mc/release/windows-amd64/mc.exe" -OutFile "mc.exe"
```

### Cấu hình mc
```bash
mc alias set local http://localhost:9000 sysadmin Thinklabs@36
```

### Tạo bucket
```bash
mc mb local/my-bucket
```

### Upload file
```bash
mc cp myfile.txt local/my-bucket/
```

### List buckets
```bash
mc ls local
```

## Deployment Configuration

### Deploy Settings
- **Replicas**: 1 (single instance)
- **Restart Policy**: On failure với 3 lần thử
- **Placement**: Chỉ chạy trên manager nodes
- **Network**: External overlay network `tradar-network`
- **Volume**: Local driver cho persistence